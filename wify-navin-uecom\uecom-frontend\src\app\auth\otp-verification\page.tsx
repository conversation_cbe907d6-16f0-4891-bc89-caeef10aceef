'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { resendOTP, verifyOTP } from '@/app/api/auth'
import { EnvironmentUtils, STATIC_OTP } from '@/utils/environment.utils'

export default function OTPVerification() {
    const [phone, setPhone] = useState<string>('')
    const [dialCode, setDialCode] = useState<string>('')
    const router = useRouter()

    const [otp, setOtp] = useState<string[]>(new Array(6).fill(''))
    const [resendTimer, setResendTimer] = useState<number>(30)
    const [canResend, setCanResend] = useState<boolean>(false)
    const [isVerified, setIsVerified] = useState<boolean>(false)
    const [errorMessage, setErrorMessage] = useState<string>('')
    const otpRefs = useRef<HTMLInputElement[]>([])

    useEffect(() => {
        otpRefs.current[0]?.focus()

        const storedPhone = localStorage.getItem('phone') || ''
        const storedDialCode = localStorage.getItem('dialCode') || ''
        setPhone(storedPhone)
        setDialCode(storedDialCode)

        const verified = localStorage.getItem('isVerified') === 'true'
        if (verified) {
            setIsVerified(true)
            router.push('/')
        }
    }, [])

    useEffect(() => {
        if (resendTimer > 0) {
            const timer = setInterval(() => setResendTimer((prev) => prev - 1), 1000)
            return () => clearInterval(timer)
        }
        setCanResend(true)
    }, [resendTimer])

    const handleOtpChange = (index: number, value: string) => {
        if (!/^\d*$/.test(value)) return
        const newOtp = [...otp]
        newOtp[index] = value
        setOtp(newOtp)

        if (index < 5 && value !== '') {
            otpRefs.current[index + 1]?.focus()
        }
    }

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace' && otp[index] === '' && index > 0) {
            otpRefs.current[index - 1]?.focus()
        }
    }

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault()
        const pastedData = e.clipboardData.getData('text').trim()
        if (/^\d{6}$/.test(pastedData)) {
            setOtp(pastedData.split(''))
            otpRefs.current[5]?.focus()
        }
    }

    const handleVerifyOTP = async () => {
        const otpCode = otp.join('')
        if (otpCode.length < 6) {
            setErrorMessage('❌ Please enter a valid 6-digit OTP.')
            return
        }

        try {
            console.log('Verifying OTP:', { phone, otpCode })
            const response = await verifyOTP(phone, otpCode)
            console.log('OTP verification response:', response)

            if (response && response.statusCode >= 200 && response.statusCode < 300) {
                setIsVerified(true)

                // Set authentication state in localStorage
                localStorage.setItem('isLoggedIn', 'true')
                localStorage.setItem('isVerified', 'true')

                // Set authentication state in cookies for server-side middleware
                document.cookie = 'isVerified=true; path=/; max-age=86400' // 24 hours

                // Redirect to discovery page
                router.push('/discovery')
            } else {
                // Handle API error response
                const errorMsg = response?.message || 'OTP verification failed'
                setErrorMessage(`❌ ${errorMsg}`)
                setOtp(new Array(6).fill(''))
                otpRefs.current[0]?.focus()
            }
        } catch (error: any) {
            console.error('OTP verification error:', error)
            setErrorMessage(`❌ ${error.message}`)
            setOtp(new Array(6).fill(''))
            otpRefs.current[0]?.focus()
        }
    }

    const handleResendOTP = async () => {
        try {
            await resendOTP(phone)
            setResendTimer(30)
            setCanResend(false)
        } catch (error: any) {
            setErrorMessage(`❌ ${error.message}`)
        }
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 px-4">
            <button
                onClick={() => router.push('/auth')}
                className="absolute top-5 left-5 text-2xl text-gray-600"
            >
                ←
            </button>

            <h2 className="text-2xl font-bold text-gray-900">Verify Your Phone Number</h2>
            <p className="text-gray-500 text-sm mt-1">A verification code has been sent to:</p>
            <p className="text-gray-500 text-sm font-semibold">
                {dialCode} {phone}
            </p>

            <div className="mt-6 flex items-center justify-center">
                <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-400 shadow-md flex items-center justify-center">
                    <Image
                        src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQnsIfE-jT4HRvYZ_MQbKmdTEVThfgrTqWbxg&s"
                        width={96}
                        height={96}
                        alt="Phone Authentication"
                        className="object-cover w-full h-full"
                    />
                </div>
            </div>

            <div className="mt-6 flex gap-2">
                {otp.map((digit, index) => (
                    <input
                        key={index}
                        ref={(el) => {
                            otpRefs.current[index] = el as HTMLInputElement
                        }}
                        type="text"
                        autoFocus
                        value={digit}
                        onChange={(e) => handleOtpChange(index, e.target.value.slice(0, 1))}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        onPaste={handlePaste}
                        maxLength={1}
                        className="w-12 h-12 text-center text-xl font-semibold border border-gray-400 rounded-md focus:outline-none focus:border-blue-500 text-black"
                    />
                ))}
            </div>

            <div className="mt-3 text-gray-500">
                {canResend ? (
                    <button onClick={handleResendOTP} className="text-blue-500 font-semibold">
                        Resend OTP
                    </button>
                ) : (
                    <p>Resend OTP in {resendTimer} seconds</p>
                )}
            </div>

            {/* Show static OTP in non-production environments */}
            {!EnvironmentUtils.isProduction() && (
                <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 rounded-md">
                    <p className="text-yellow-800 font-medium">
                        <span className="font-bold">Development Mode:</span> Use static OTP{' '}
                        <span className="font-mono bg-yellow-200 px-2 py-1 rounded">
                            {STATIC_OTP}
                        </span>
                    </p>
                    <p className="text-xs text-yellow-700 mt-1">
                        This static OTP is only available in non-production environments.
                    </p>
                    <button
                        onClick={() => {
                            // Auto-fill the OTP fields with the static OTP
                            const staticOtpArray = STATIC_OTP.split('')
                            setOtp(staticOtpArray)
                        }}
                        className="mt-2 text-xs bg-yellow-200 hover:bg-yellow-300 text-yellow-800 font-medium py-1 px-2 rounded"
                    >
                        Auto-fill OTP
                    </button>
                </div>
            )}

            <button
                onClick={handleVerifyOTP}
                disabled={otp.join('').length < 6}
                className={`mt-6 py-3 w-full max-w-sm text-white font-semibold rounded-lg shadow-md transition duration-300 ${
                    otp.join('').length === 6
                        ? 'bg-[#162D50] hover:bg-[#0F1E3C]'
                        : 'bg-gray-400 cursor-not-allowed'
                }`}
            >
                Verify OTP
            </button>

            {errorMessage && !isVerified && <p className="mt-3 text-red-500">{errorMessage}</p>}
            {isVerified && <p className="mt-3 text-green-500">✅ OTP Verified Successfully!</p>}
        </div>
    )
}
