'use client'

import React, { useState, useEffect, Suspense, useMemo } from 'react'
import { SectionData } from '../../types'
import ErrorBoundary from '../ui/molecules/ErrorBoundary'

interface ClientSectionRendererProps {
    section: SectionData
    index: number
    loadingFallback: React.ReactNode
    errorFallback: React.ReactNode
    children: React.ReactNode
    disableLoading?: boolean
}

/**
 * Client-side wrapper for section rendering with shimmer effect
 * Handles the loading state transition on the client
 */
export function ClientSectionRenderer({
    section,
    index,
    loadingFallback,
    errorFallback,
    children,
    disableLoading = false,
}: ClientSectionRendererProps) {
    // State to manage skeleton loading time
    const [isLoading, setIsLoading] = useState(!disableLoading)

    // State to force re-render on error recovery
    const [retryKey, setRetryKey] = useState(0)

    // Handler for retrying after an error
    const handleRetry = () => {
        // Increment key to force re-render
        setRetryKey((prevKey) => prevKey + 1)
        // Reset loading state
        setIsLoading(true)
    }

    // Effect to control skeleton loading time (only if loading is enabled)
    useEffect(() => {
        // Skip loading effect if disabled
        if (disableLoading) {
            return
        }

        const timer = setTimeout(() => {
            setIsLoading(false)
        }, 2500) // 2.5 seconds loading time for all sections

        return () => clearTimeout(timer)
    }, [disableLoading, retryKey]) // Add retryKey to dependencies to reset loading on retry

    // Generate a unique key for the section
    const key = `${section.type}-${index}-${retryKey}`

    // Create enhanced error fallback with retry capability
    const enhancedErrorFallback = useMemo(() => {
        // Ensure errorFallback is a valid React element and is a component that accepts props
        if (React.isValidElement(errorFallback) && typeof errorFallback.type !== 'string') {
            return React.cloneElement(
                errorFallback as React.ReactElement<{ onRetry: () => void }>,
                { onRetry: handleRetry },
            )
        }

        return errorFallback
    }, [errorFallback, handleRetry])

    // Special handling for header to ensure it's visible at the top
    if (section.type === 'header') {
        return (
            <div className="w-full sticky top-0 z-50 bg-white shadow-sm" key={key}>
                {isLoading ? (
                    loadingFallback
                ) : (
                    <Suspense fallback={loadingFallback}>
                        <ErrorBoundary
                            fallback={enhancedErrorFallback}
                            onError={(error) => {
                                console.error(`Error in header section:`, error)
                            }}
                        >
                            {children}
                        </ErrorBoundary>
                    </Suspense>
                )}
            </div>
        )
    }

    // Special handling for footer to ensure it's at the bottom
    if (section.type === 'footer') {
        return (
            <div className="w-full mt-auto" key={key}>
                {isLoading ? (
                    loadingFallback
                ) : (
                    <Suspense fallback={loadingFallback}>
                        <ErrorBoundary
                            fallback={enhancedErrorFallback}
                            onError={(error) => {
                                console.error(`Error in footer section:`, error)
                            }}
                        >
                            {children}
                        </ErrorBoundary>
                    </Suspense>
                )}
            </div>
        )
    }

    // Normal handling for other components
    return (
        <div className="w-full" key={key}>
            {isLoading ? (
                loadingFallback
            ) : (
                <Suspense fallback={loadingFallback}>
                    <ErrorBoundary
                        fallback={enhancedErrorFallback}
                        onError={(error) => {
                            console.error(`Error in ${section.type} section:`, error)
                        }}
                    >
                        {children}
                    </ErrorBoundary>
                </Suspense>
            )}
        </div>
    )
}
