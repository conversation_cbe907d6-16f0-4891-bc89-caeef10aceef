interface PhoneInputProps {
    value: string | undefined
    onChange: (value: string | undefined) => void
}

export default function PhoneInput({ value, onChange }: PhoneInputProps) {
    return (
        <div className="flex items-center border border-gray-400 rounded-lg overflow-hidden w-full bg-white">
            <div className="flex items-center px-2 bg-white">
                <img
                    src="https://flagcdn.com/w40/in.png"
                    onError={
                        typeof window !== 'undefined'
                            ? (e) => (e.currentTarget.src = '/fallback-flag.png')
                            : undefined
                    }
                    alt="India"
                    width={24}
                    height={16}
                    className="rounded mr-2"
                />
                <span className="font-medium text-black">+91</span>
            </div>
            <div className="flex-grow">
                <input
                    type="tel"
                    value={value ?? ''}
                    onChange={(e) => {
                        const numericValue = e.target.value.replace(/\D/g, '')
                        onChange(numericValue.slice(0, 10))
                    }}
                    placeholder="Enter phone number"
                    className="w-full p-2 outline-none text-black bg-white"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={10}
                />
            </div>
        </div>
    )
}
