/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './src/app/**/*.{js,ts,jsx,tsx,mdx}',
        './src/components/**/*.{js,ts,jsx,tsx,mdx}',
        './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
        './src/features/**/*.{js,ts,jsx,tsx,mdx}',
        './src/providers/**/*.{js,ts,jsx,tsx,mdx}',
        './src/hooks/**/*.{js,ts,jsx,tsx,mdx}',
    ],
    theme: {
        extend: {
            colors: {
                'blue-primary': 'var(--blue-primary)',
                'orange-primary': 'var(--orange-primary)',
                'grey-01': 'var(--grey-01)',
                'grey-02': 'var(--grey-02)',
                'grey-03': 'var(--grey-03)',
                'grey-04': 'var(--grey-04)',
                'blue-60': 'var(--blue-60)',
                'blue-90': 'var(--blue-90)',
                'orange-10': 'var(--orange-10)',
                background: 'var(--background)',
                foreground: 'var(--foreground)',
            },
            fontFamily: {
                poppins: ['Poppins', 'sans-serif'],
            },
            animation: {
                'fade-in': 'fadeIn 0.3s ease-out',
                'slide-in-from-bottom': 'slideInFromBottom 0.3s ease-out',
                'zoom-in': 'zoomIn 0.3s ease-out',
                'animate-in':
                    'fadeIn 0.3s ease-out, slideInFromBottom 0.3s ease-out, zoomIn 0.3s ease-out',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideInFromBottom: {
                    '0%': { transform: 'translateY(20px)' },
                    '100%': { transform: 'translateY(0)' },
                },
                zoomIn: {
                    '0%': { transform: 'scale(0.95)' },
                    '100%': { transform: 'scale(1)' },
                },
            },
        },
    },
    plugins: [],
}
