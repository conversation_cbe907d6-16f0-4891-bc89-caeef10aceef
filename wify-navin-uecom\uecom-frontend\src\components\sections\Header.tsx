'use client'

import React, { useMemo } from 'react'
import { HeaderSection } from '../../types'
import { Bell, ShoppingCart, MapPin } from 'lucide-react'
import Link from 'next/link'
import { useDeviceContext } from '../../providers/DeviceProvider'
import useLocationFetcher from '@/hooks/useLocationFetcher'
import { useStaticText } from '@/hooks/useStaticText'

interface HeaderProps {
    data: HeaderSection | null
}

/**
 * Header component with responsive design
 * Uses useDeviceContext hook for device detection
 * Enhanced with accessibility features and internationalization
 */
const Header: React.FC<HeaderProps> = ({ data }) => {
    // Use our custom hook for device detection
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    // Use static text instead of i18n
    const { t } = useStaticText()

    // Default values for when data is null - memoized to prevent recreation
    const defaultData = useMemo(() => {
        // Get text values from static text hook
        const shopLabel = t('common.shop')
        const cartLabel = t('common.cart')
        const accountLabel = t('common.account')
        const businessLink = t('home.for_business')

        return {
            location: 'Location',
            Image: '/assets/images/wify_logo.png',
            logoText: 'Wify',
            navLinks: [
                { href: '/shop', label: shopLabel },
                { href: '/cart', label: cartLabel },
                { href: '/profile', label: accountLabel },
            ],
            businessLink: businessLink,
        }
    }, [t])

    // Use provided data or default data
    const headerData = data?.value || defaultData

    // Use the location fetcher hook to get actual location details
    const { locationDetails } = useLocationFetcher()

    // Get the postal code from location details or fall back to the data value
    const postalCode = locationDetails?.postalCode || headerData.location

    // Prepare loading state for when client-side hydration is not complete
    // We'll use this in the render method instead of early return to avoid breaking rules of hooks
    const isLoading = !isMounted

    // No accessibility menu for mobile

    // Mobile view component
    const MobileView = () => (
        <header className="bg-grey-03 pt-4 mb-2 block sm:block md:hidden">
            <div className="container mx-auto px-4">
                {/* Top bar with logo and icons */}
                <div className="flex justify-between items-center min-h-[40px]">
                    {/* Logo or empty placeholder to preserve space */}
                    <div className="flex-shrink-0">
                        <Link href="/discovery">
                            <img
                                src={headerData.Image}
                                alt={headerData.logoText}
                                className="h-8 w-auto"
                            />
                        </Link>
                    </div>

                    {/* Spacer to push icons to the end if logo is missing */}
                    <div className="flex-grow" />

                    {/* Icons */}
                    <div className="flex items-center space-x-4">
                        <button className="relative" aria-label={t('common.notifications')}>
                            <Bell size={30} className="text-blue-primary" />
                        </button>
                        <button className="relative" aria-label={t('common.cart')}>
                            <ShoppingCart size={30} className="text-blue-primary" />
                            <span className="absolute -top-1 -right-1 bg-orange-primary text-white text-[10px] rounded-full h-5 w-5 flex items-center justify-center font-medium">
                                2
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </header>
    )

    // Tablet view component
    const TabletView = () => (
        <header className="w-full bg-white shadow-sm hidden md:block lg:hidden">
            <div className="container mx-auto px-5 h-[65px] flex items-center">
                <div className="flex items-center justify-between w-full">
                    <Link href="/discovery" className="flex items-center flex-shrink-0 mr-3">
                        <img
                            src={headerData.Image}
                            alt={headerData.logoText}
                            className="h-9 w-auto"
                        />
                    </Link>
                    <div className="flex items-center space-x-3 flex-nowrap overflow-x-auto">
                        <div className="flex items-center gap-1 text-blue-60 font-poppins font-medium flex-shrink-0">
                            <MapPin className="h-4 w-4 text-blue" />
                            <span className="text-[14px] text-blue-primary whitespace-nowrap">
                                {postalCode}
                            </span>
                        </div>
                        <nav className="space-x-4 flex-shrink-0">
                            {headerData.navLinks.map((link, index) => (
                                <Link
                                    key={index}
                                    href={link.href}
                                    className="text-[14px] text-blue hover:text-blue-60 font-poppins font-medium whitespace-nowrap flex-shrink-0"
                                >
                                    {link.label}
                                </Link>
                            ))}
                        </nav>

                        {/* Notifications */}
                        <button
                            className="relative flex-shrink-0"
                            aria-label={t('common.notifications')}
                        >
                            <Bell className="h-5 w-5 text-blue" />
                            <span className="absolute -top-1 -right-1 flex items-center justify-center w-[12px] h-[12px] bg-orange text-white text-xs font-bold rounded-full">
                                3
                            </span>
                        </button>

                        {/* For Business button */}
                        <button
                            type="button"
                            className="text-white bg-gradient-to-r from-blue-900 to-blue-600
                            font-medium rounded-xl text-sm px-5 py-2 whitespace-nowrap
                            shadow-lg focus:outline-none focus:ring-2 flex-shrink-0
                            focus:ring-blue-400 font-poppins font-medium"
                        >
                            {headerData.businessLink}
                        </button>
                    </div>
                </div>
            </div>
        </header>
    )

    // Desktop view component
    const DesktopView = () => (
        <header className="w-full bg-white shadow-sm hidden lg:block">
            <div className="container mx-auto px-4 h-[72px] flex items-center">
                <div className="flex items-center justify-between w-full">
                    <Link href="/discovery" className="flex items-center flex-shrink-0 mr-4">
                        <img
                            src={headerData.Image}
                            alt={headerData.logoText}
                            className="h-10 w-auto"
                        />
                    </Link>
                    <div className="flex items-center space-x-4 lg:space-x-6 flex-nowrap overflow-x-auto">
                        <div className="flex items-center gap-2 text-blue-60 font-poppins font-medium flex-shrink-0">
                            <MapPin className="h-5 w-5 text-blue" />
                            <span className="text-[15px] text-blue-primary whitespace-nowrap">
                                {postalCode}
                            </span>
                        </div>
                        <nav className="space-x-6 flex-shrink-0">
                            {headerData.navLinks.map((link, index) => (
                                <Link
                                    key={index}
                                    href={link.href}
                                    className="text-[15px] text-blue hover:text-blue-60 font-poppins font-medium whitespace-nowrap flex-shrink-0"
                                >
                                    {link.label}
                                </Link>
                            ))}
                        </nav>

                        {/* Notifications */}
                        <button
                            className="relative flex-shrink-0"
                            aria-label={t('common.notifications')}
                        >
                            <Bell className="h-5 w-5 text-blue" />
                            <span className="absolute -top-1 -right-1 flex items-center justify-center w-[12px] h-[12px] bg-orange text-white text-xs font-bold rounded-full">
                                3
                            </span>
                        </button>

                        {/* For Business button */}
                        <button
                            type="button"
                            className="text-white bg-gradient-to-r from-blue-900 to-blue-600
                            font-medium rounded-xl text-sm px-4 sm:px-6 md:px-8 py-2 md:py-3 whitespace-nowrap
                            shadow-lg focus:outline-none focus:ring-2 flex-shrink-0
                            focus:ring-blue-400 font-poppins font-medium"
                        >
                            {headerData.businessLink}
                        </button>
                    </div>
                </div>
            </div>
        </header>
    )

    // Render the appropriate header based on device type
    if (!isMounted) return <div className="h-16 bg-white">Header loading...</div>
    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}

export default Header
