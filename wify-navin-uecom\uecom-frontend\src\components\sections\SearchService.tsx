'use client'

import React from 'react'
import { SearchServiceSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'
import { motion } from 'framer-motion'
import { Search, MapPin } from 'lucide-react'
import { Button } from '../../components/ui/atoms/Button'
import useLocationFetcher from '../../hooks/useLocationFetcher'

interface SearchServiceProps {
    data: SearchServiceSection
}

export default function SearchService({ data }: SearchServiceProps) {
    const { isMounted, isMobile, isTablet } = useDeviceContext()
    const [searchQuery, setSearchQuery] = React.useState('')
    const { locationDetails } = useLocationFetcher()
    const postalCode = locationDetails?.postalCode || data.value.location

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: { staggerChildren: 0.1, delayChildren: 0.2 },
        },
    }

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: { type: 'spring', stiffness: 100 },
        },
    }

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault()
        console.log('Searching for:', searchQuery)
    }

    // Shared Input Component
    const SearchInput = ({
        placeholder,
        buttonText,
    }: {
        placeholder: string
        buttonText: string
    }) => (
        <form className="relative" onSubmit={handleSearch}>
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search size={20} className="text-gray-400" />
            </div>
            <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={placeholder}
                className="block w-full pl-12 pr-4 py-4 border border-gray-200 rounded-[14px] bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-primary focus:border-transparent text-gray-700 placeholder-gray-400"
                aria-label={placeholder}
            />
            <Button
                type="submit"
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-primary text-white px-6 py-2 rounded-[10px] hover:bg-blue-600 transition-colors duration-300"
            >
                {buttonText}
            </Button>
        </form>
    )

    const MobileView = () => (
        <motion.section
            className="container mx-auto px-2 pb-4 border-none rounded-b-[30px] bg-gradient-to-b from-white to-sky-200 relative z-10 block sm:block md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
        >
            <motion.div
                className="mb-4 px-2"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <motion.div className="flex items-center mb-2" variants={itemVariants}>
                    <MapPin size={20} className="text-gray-600" />
                    <span className="text-gray-700 font-medium border-b border-gray-700 ml-1 text-sm text-gray-500">
                        {postalCode}
                    </span>
                </motion.div>
                <motion.h1
                    className="text-[24px] font-bold text-blue-primary leading-tight"
                    variants={itemVariants}
                >
                    {data.value.title}
                </motion.h1>
                <motion.p
                    className="text-blue-primary mt-1 font-medium text-[14px]"
                    variants={itemVariants}
                >
                    {data.value.subheading}
                </motion.p>
            </motion.div>
            <motion.div className="relative px-2" variants={itemVariants}>
                <SearchInput
                    placeholder={data.value.searchPlaceholder}
                    buttonText={data.value.buttonText}
                />
            </motion.div>
        </motion.section>
    )

    const TabletView = () => (
        <motion.section
            className="container mx-auto px-3 py-6 border-none bg-gradient-to-b from-white to-sky-200 relative z-10 hidden md:block lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
        >
            <motion.div
                className="text-center max-w-2xl mx-auto mb-6"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <motion.div
                    className="flex items-center justify-center mb-3"
                    variants={itemVariants}
                >
                    <MapPin size={18} className="text-gray-600" />
                    <span className="text-gray-700 font-medium border-b border-gray-700 ml-1 text-sm">
                        {postalCode}
                    </span>
                </motion.div>
                <motion.h1
                    className="text-[30px] font-bold text-blue-primary leading-tight mb-2"
                    variants={itemVariants}
                >
                    {data.value.title}
                </motion.h1>
                <motion.p
                    className="text-blue-primary font-medium text-[16px] mt-2 mb-5"
                    variants={itemVariants}
                >
                    {data.value.subheading}
                </motion.p>
            </motion.div>
            <motion.div className="max-w-3xl mx-auto" variants={itemVariants}>
                <SearchInput
                    placeholder={data.value.searchPlaceholder}
                    buttonText={data.value.buttonText}
                />
            </motion.div>
        </motion.section>
    )

    const DesktopView = () => (
        <motion.section
            className="container mx-auto px-4 py-8 border-none bg-gradient-to-b from-white to-sky-200 relative z-10 hidden lg:block"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
        >
            <motion.div
                className="text-center max-w-3xl mx-auto mb-8"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
            >
                <motion.h1
                    className="text-[36px] font-bold text-blue-primary leading-tight mb-3"
                    variants={itemVariants}
                >
                    {data.value.title}
                </motion.h1>
                <motion.p
                    className="text-blue-primary font-medium text-[18px] mt-2 mb-6"
                    variants={itemVariants}
                >
                    {data.value.subheading}
                </motion.p>
            </motion.div>
            <motion.div className="max-w-4xl mx-auto" variants={itemVariants}>
                <SearchInput
                    placeholder={data.value.searchPlaceholder}
                    buttonText={data.value.buttonText}
                />
            </motion.div>
        </motion.section>
    )

    if (!isMounted) return null

    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}
